# ThinkingData Analytics SDK (CommonJS Compatible)

这是一个支持 CommonJS `require()` 加载的 ThinkingData 数据分析 SDK，基于原始 SDK 版本 ******* 重构。

## 特性

- ✅ **通用模块定义 (UMD)**: 支持 CommonJS、AMD 和浏览器全局变量
- ✅ **完整功能**: 包含原始 SDK 的所有核心功能
- ✅ **事件追踪**: 支持自定义事件追踪和属性设置
- ✅ **用户管理**: 用户识别、登录、属性管理
- ✅ **数据持久化**: 本地存储用户数据和配置
- ✅ **批量发送**: 优化网络请求性能
- ✅ **调试模式**: 支持开发和调试模式

## 安装和使用

### CommonJS 环境 (Node.js)

```javascript
const ThinkingDataAPI = require('./sdk-MIX-KS-require.js');

const ta = ThinkingDataAPI.init({
    appId: 'your_app_id',
    serverUrl: 'https://your-server.com',
    enableLog: true
});

ta.track('test_event', {
    property1: 'value1',
    property2: 123
});
```

### 浏览器环境

```html
<script src="sdk-MIX-KS-require.js"></script>
<script>
const ta = ThinkingDataAPI.init({
    appId: 'your_app_id',
    serverUrl: 'https://your-server.com'
});

ta.track('page_view', {
    page_name: 'home'
});
</script>
```

### AMD 环境

```javascript
define(['sdk-MIX-KS-require'], function(ThinkingDataAPI) {
    const ta = ThinkingDataAPI.init({
        appId: 'your_app_id',
        serverUrl: 'https://your-server.com'
    });
    
    ta.track('module_loaded', {
        module_name: 'analytics'
    });
});
```

## API 文档

### 初始化

```javascript
const ta = ThinkingDataAPI.init(config);
```

**配置参数:**
- `appId`: 应用ID (必需)
- `serverUrl`: 服务器地址 (必需)
- `enableLog`: 是否启用日志 (默认: true)
- `debugMode`: 调试模式 ('none', 'debug', 'debugOnly')
- `enablePersistence`: 是否启用数据持久化 (默认: true)
- `strict`: 是否启用严格模式 (默认: false)

### 事件追踪

```javascript
// 基础事件追踪
ta.track('event_name', {
    property1: 'value1',
    property2: 123
});

// 带时间的事件追踪
ta.track('event_name', properties, new Date(), callback);

// 事件计时
ta.timeEvent('page_view');
// ... 一段时间后
ta.track('page_view', { page_name: 'home' }); // 自动计算duration
```

### 用户管理

```javascript
// 用户识别
ta.identify('user_12345');

// 用户登录
ta.login('account_67890');

// 用户登出
ta.logout();

// 获取用户ID
const distinctId = ta.getDistinctId();
const deviceId = ta.getDeviceId();
```

### 用户属性

```javascript
// 设置用户属性
ta.userSet({
    name: '张三',
    age: 25,
    city: '北京'
});

// 设置用户属性（仅一次）
ta.userSetOnce({
    first_visit_time: new Date()
});

// 用户数值属性累加
ta.userAdd({
    login_count: 1,
    total_revenue: 99.99
});

// 用户数组属性追加
ta.userAppend({
    favorite_categories: ['electronics']
});

// 删除用户属性
ta.userUnset('temp_property');

// 删除用户
ta.userDel();
```

### 公共属性

```javascript
// 设置公共属性
ta.setSuperProperties({
    platform: 'web',
    version: '1.0.0'
});

// 获取公共属性
const superProps = ta.getSuperProperties();

// 清除公共属性
ta.clearSuperProperties();
```

### 追踪控制

```javascript
// 暂停追踪
ta.disableTracking();

// 恢复追踪
ta.enableTracking();

// 选择退出追踪
ta.optOutTracking();

// 选择加入追踪
ta.optInTracking();

// 立即发送数据
ta.flush();
```

## 数据格式

### 事件数据结构

```javascript
{
    "#type": "track",
    "#time": "2023-12-01 10:30:00.123",
    "#distinct_id": "user_12345",
    "#account_id": "account_67890", // 可选
    "#event_name": "purchase",
    "properties": {
        "#zone_offset": 8,
        "#lib": "MG",
        "#lib_version": "*******",
        "#device_id": "device_abc123",
        "product_name": "iPhone",
        "price": 999.99
    }
}
```

### 用户属性数据结构

```javascript
{
    "#type": "user_set",
    "#time": "2023-12-01 10:30:00.123",
    "#distinct_id": "user_12345",
    "properties": {
        "name": "张三",
        "age": 25,
        "city": "北京"
    }
}
```

## 错误处理

SDK 内置了完善的错误处理机制：

- 参数验证：自动验证事件名称和属性格式
- 网络重试：支持自动重试失败的请求
- 数据校验：确保数据格式正确
- 日志记录：详细的调试信息

## 兼容性

- **浏览器**: 支持现代浏览器 (IE9+)
- **Node.js**: 支持 Node.js 环境
- **小程序**: 兼容各种小程序平台
- **模块系统**: 支持 CommonJS、AMD、UMD

## 与原始SDK的差异

1. **模块化支持**: 新增 CommonJS require() 支持
2. **UMD 包装**: 支持多种模块加载方式
3. **代码结构**: 重新组织代码结构，提高可读性
4. **API 兼容**: 保持与原始 SDK 的 API 兼容性

## 注意事项

1. 确保在使用前正确配置 `appId` 和 `serverUrl`
2. 在生产环境中建议关闭调试日志
3. 事件名称和属性名称需要符合命名规范
4. 建议定期调用 `flush()` 确保数据及时发送

## 示例项目

查看 `sdk-usage-example.js` 文件获取完整的使用示例。
