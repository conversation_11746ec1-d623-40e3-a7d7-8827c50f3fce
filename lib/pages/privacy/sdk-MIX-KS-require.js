/**
 * ThinkingData Analytics SDK for MIX-KS (CommonJS Compatible Version)
 * Supports both require() loading and direct script inclusion
 * Based on original SDK version *******
 */

(function (root, factory) {
    // Universal Module Definition (UMD) pattern
    if (typeof exports === 'object' && typeof module !== 'undefined') {
        // CommonJS/Node.js environment
        module.exports = factory();
    } else if (typeof define === 'function' && define.amd) {
        // AMD environment
        define([], factory);
    } else {
        // Browser global environment
        root.ThinkingDataAPI = factory();
    }
}(typeof self !== 'undefined' ? self : this, function () {
    'use strict';

    // Original SDK code - complete implementation
    function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),e}
    
    var Config={LIB_VERSION:"*******",LIB_NAME:"MG"},_={},ArrayProto=Array.prototype,ObjProto=Object.prototype,slice=ArrayProto.slice,nativeToString=ObjProto.toString,nativeHasOwnProperty=Object.prototype.hasOwnProperty,nativeForEach=ArrayProto.forEach,nativeIsArray=Array.isArray,breaker={},utmTypes=["utm_source","utm_medium","utm_campaign","utm_content","utm_term"];
    
    // Utility functions
    _.each=function(e,t,n){if(null==e)return!1;if(nativeForEach&&e.forEach===nativeForEach)e.forEach(t,n);else if(e.length===+e.length){for(var i=0,a=e.length;i<a;i++)if(i in e&&t.call(n,e[i],i,e)===breaker)return!1}else for(var r in e)if(nativeHasOwnProperty.call(e,r)&&t.call(n,e[r],r,e)===breaker)return!1};
    _.extend=function(n){return _.each(slice.call(arguments,1),function(e){for(var t in e)void 0!==e[t]&&(n[t]=e[t])}),n};
    _.extend2Layers=function(n){return _.each(slice.call(arguments,1),function(e){for(var t in e)void 0!==e[t]&&(_.isObject(e[t])&&_.isObject(n[t])?_.extend(n[t],e[t]):n[t]=e[t])}),n};
    _.isArray=nativeIsArray||function(e){return"[object Array]"===nativeToString.call(e)};
    _.isFunction=function(e){try{return"function"==typeof e}catch(e){return!1}};
    _.isPromise=function(e){return"[object Promise]"===nativeToString.call(e)&&null!=e};
    _.isObject=function(e){return"[object Object]"===nativeToString.call(e)&&null!=e};
    _.isEmptyObject=function(e){if(_.isObject(e)){for(var t in e)if(nativeHasOwnProperty.call(e,t))return!1;return!0}return!1};
    _.isUndefined=function(e){return void 0===e};
    _.isString=function(e){return"[object String]"===nativeToString.call(e)};
    _.isDate=function(e){return"[object Date]"===nativeToString.call(e)};
    _.isBoolean=function(e){return"[object Boolean]"===nativeToString.call(e)};
    _.isNumber=function(e){return"[object Number]"===nativeToString.call(e)&&/[\d\.]+/.test(String(e))};
    _.isJSONString=function(e){try{JSON.parse(e)}catch(e){return!1}return!0};
    _.decodeURIComponent=function(t){var n="";try{n=decodeURIComponent(t)}catch(e){n=t}return n};
    _.encodeURIComponent=function(t){var n="";try{n=encodeURIComponent(t)}catch(e){n=t}return n};
    
    // Date and encoding functions
    _.formatDate=function(e){function t(e){return e<10?"0"+e:e}return e.getFullYear()+"-"+t(e.getMonth()+1)+"-"+t(e.getDate())+" "+t(e.getHours())+":"+t(e.getMinutes())+":"+t(e.getSeconds())+"."+((n=e.getMilliseconds())<100&&9<n?"0"+n:n<10?"00"+n:n);var n};
    _.formatTimeZone=function(e,t){if("number"!=typeof t)return e;var n=e.getTime(),e=6e4*e.getTimezoneOffset();return new Date(n+e+36e5*t)};
    _.getTimeZone=function(e,t){return"number"==typeof t?t:0-e.getTimezoneOffset()/60};
    _.searchObjDate=function(n,i){try{(_.isObject(n)||_.isArray(n))&&_.each(n,function(e,t){_.isObject(e)||_.isArray(e)?_.searchObjDate(n[t],i):_.isDate(e)&&(n[t]=_.formatDate(_.formatTimeZone(e,i)))})}catch(e){logger.warn(e)}};
    
    // UUID functions
    _.UUID=function(){var e=(new Date).getTime();return String(Math.random()).replace(".","").slice(1,11)+"-"+e};
    _.UUIDv4=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})};
    
    // Platform functions
    _.setMpPlatform=function(e){_.mpPlatform=e};
    _.getMpPlatform=function(){return _.mpPlatform};
    _.createExtraHeaders=function(){return{"TA-Integration-Type":Config.LIB_NAME,"TA-Integration-Version":Config.LIB_VERSION,"TA-Integration-Count":"1","TA-Integration-Extra":_.getMpPlatform()}};
    _.checkAppId=function(e){return e=e.replace(/\s*/g,"")};
    _.checkUrl=function(e){return e=e.replace(/\s*/g,""),e=_.url("basic",e)};
    
    // URL parsing function (simplified)
    _.url=function(){
        return function(e,t){
            var n={};
            if(t=t||window.location.toString(),!e)return t;
            if(e=e.toString(),t.match(/^mailto:([^/].+)/)){
                var i=t.match(/^mailto:([^/].+)/);
                n.protocol="mailto",n.email=i[1];
            }else{
                if(t.match(/(.*?)#(.*)/)&&(i=t.match(/(.*?)#(.*)/),n.hash=i[2],t=i[1]),t.match(/(.*?)\?(.*)/)&&(i=t.match(/(.*?)\?(.*)/),n.query=i[2],t=i[1]),t.match(/(.*?):?\/\/(.*)/)&&(i=t.match(/(.*?):?\/\/(.*)/),n.protocol=i[1].toLowerCase(),t=i[2]),t.match(/(.*?)(\/.*)/)&&(i=t.match(/(.*?)(\/.*)/),n.path=i[2],t=i[1]),n.hostname=t.toLowerCase(),n.protocol=n.protocol||window.location.protocol.replace(":",""),n.basic=n.protocol+"://"+n.hostname)
            }
            return e in n?n[e]:"{}"===e?n:""
        }
    }();
    
    // UTM and query functions
    _.getQueryParam=function(e,t){t=t.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]"),e=_.decodeURIComponent(e);e=new RegExp("[\\?&]"+t+"=([^&#]*)").exec(e);return null===e||e&&"string"!=typeof e[1]&&e[1].length?"":_.decodeURIComponent(e[1])};
    _.getUtmFromQuery=function(t){var n={};return _.each(utmTypes,function(e){t[e]&&(n[e]=t[e])}),JSON.stringify(n)};
    _.indexOf=function(e,t){var n=e.indexOf;if(n)return n.call(e,t);for(var i=0;i<e.length;i++)if(t===e[i])return i;return-1};
    _.checkCalibration=function(e,t,n){return e};
    
    // Logger implementation
    var logger="object"===_typeof(logger)?logger:{};
    logger.info=function(){if("object"===("undefined"==typeof console?"undefined":_typeof(console))&&console.log&&logger.enabled)try{return arguments[0]="[ThinkingData] Info: "+arguments[0],console.log.apply(console,arguments)}catch(e){console.log("[ThinkingData] Info: "+arguments[0])}};
    logger.warn=function(){if("object"===("undefined"==typeof console?"undefined":_typeof(console))&&console.log&&logger.enabled)try{return arguments[0]="[ThinkingData] Warning: "+arguments[0],console.warn.apply(console,arguments)}catch(e){console.warn("[ThinkingData] Warning: "+arguments[0])}};
    
    // Property validation
    var KEY_NAME_MATCH_REGEX=/^[a-zA-Z][a-zA-Z0-9_]{0,49}$/;
    var PropertyChecker=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"stripProperties",value:function(e){return _.isObject(e)&&_.each(e,function(e,t){_.isString(e)||_.isNumber(e)||_.isDate(e)||_.isBoolean(e)||_.isArray(e)||_.isObject(e)||logger.warn("Your data -",t,e,"- format does not meet requirements and may not be stored correctly. Attribute values only support String, Number, Date, Boolean, Array, Object")}),e}},{key:"_checkPropertiesKey",value:function(e){var n=!0;return _.each(e,function(e,t){KEY_NAME_MATCH_REGEX.test(t)||(logger.warn("Invalid KEY: "+t),n=!1)}),n}},{key:"event",value:function(e){return!(!_.isString(e)||!KEY_NAME_MATCH_REGEX.test(e))||(logger.warn("Check the parameter format. The eventName must start with an English letter and contain no more than 50 characters including letters, digits, and underscores: "+e),!1)}},{key:"propertyName",value:function(e){return!(!_.isString(e)||!KEY_NAME_MATCH_REGEX.test(e))||(logger.warn("Check the parameter format. PropertyName must start with a letter and contain letters, digits, and underscores (_). The value is a string of no more than 50 characters: "+e),!1)}},{key:"properties",value:function(e){return this.stripProperties(e),!e||(_.isObject(e)?!!this._checkPropertiesKey(e)||(logger.warn("Check the parameter format. The properties key must start with a letter, contain digits, letters, and underscores (_), and contain a maximum of 50 characters"),!1):(logger.warn("properties can be none, but it must be an object"),!1))}},{key:"propertiesMust",value:function(e){return this.stripProperties(e),void 0===e||!_.isObject(e)||_.isEmptyObject(e)?(logger.warn("properties must be an object with a value"),!1):!!this._checkPropertiesKey(e)||(logger.warn("Check the parameter format. The properties key must start with a letter, contain digits, letters, and underscores (_), and contain a maximum of 50 characters"),!1)}},{key:"userId",value:function(e){return!(!_.isString(e)||!/^.{1,64}$/.test(e))||(logger.warn("The user ID must be a string of less than 64 characters and cannot be null"),!1)}},{key:"userAddProperties",value:function(e){if(!this.propertiesMust(e))return!1;for(var t in e)if(!_.isNumber(e[t]))return logger.warn("The attributes of userAdd need to be Number"),!1;return!0}},{key:"userAppendProperties",value:function(e){if(!this.propertiesMust(e))return!1;for(var t in e)if(!_.isArray(e[t]))return logger.warn("The attribute of userAppend must be Array"),!1;return!0}}]),e}();
    
    // Platform proxy for web environment
    var PlatformProxy=function(){function e(){_classCallCheck(this,e),this.config={persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_mg"}}return _createClass(e,[{key:"initSdkConfig",value:function(e){this.initConfig=e}},{key:"getConfig",value:function(){return this.config}},{key:"getStorage",value:function(e,t,n){e=localStorage.getItem(e);if(!t)return _.isJSONString(e)?JSON.parse(e):{};_.isJSONString(e)?n(JSON.parse(e)):n({})}},{key:"setStorage",value:function(e,t){localStorage.setItem(e,t)}},{key:"removeStorage",value:function(e){localStorage.removeItem(e)}},{key:"_setSystemProxy",value:function(e){this._sysCallback=e}},{key:"getSystemInfo",value:function(e){var t={mp_platform:"web",system:this._getOs(),screenWidth:window.screen.width,screenHeight:window.screen.height,systemLanguage:navigator.language};this._sysCallback&&(t=_.extend(t,this._sysCallback(e))),e.success(t),e.complete()}},{key:"_getOs",value:function(){var e=navigator.userAgent;return/Windows/i.test(e)?/Phone/.test(e)||/WPDesktop/.test(e)?"Windows Phone":"Windows":/(iPhone|iPad|iPod)/.test(e)?"iOS":/Android/.test(e)?"Android":/(BlackBerry|PlayBook|BB10)/i.test(e)?"BlackBerry":/Mac/i.test(e)?"MacOS":/Linux/.test(e)?"Linux":/CrOS/.test(e)?"ChromeOS":""}},{key:"getNetworkType",value:function(e){e.complete()}},{key:"onNetworkStatusChange",value:function(){}},{key:"request",value:function(e){var t={},n=new XMLHttpRequest;if(n.open(e.method,e.url),e.header)for(var i in e.header)n.setRequestHeader(i,e.header[i]);return n.onreadystatechange=function(){4===n.readyState&&200===n.status?(t.statusCode=200,_.isJSONString(n.responseText)&&(t.data=JSON.parse(n.responseText)),e.success(t)):200!==n.status&&(t.errMsg="network error",e.fail(t))},n.ontimeout=function(){t.errMsg="timeout",e.fail(t)},n.send(e.data),n}}],[{key:"createInstance",value:function(){return new e}}]),e}();
    
    // Default configuration
    var DEFAULT_CONFIG={name:"thinkingdata",is_plugin:!1,maxRetries:3,sendTimeout:3e3,enablePersistence:!0,asyncPersistence:!1,enableLog:!0,strict:!1,debugMode:"none",enableCalibrationTime:!1,enableBatch:!1,cloudEnv:"online"};
    
    // System information
    var systemInformation={properties:{"#lib":Config.LIB_NAME,"#lib_version":Config.LIB_VERSION},initDeviceId:function(e){_.isString(e)&&(this.properties["#device_id"]=e)},getSystemInfo:function(e){var n=this;PlatformAPI.onNetworkStatusChange(function(e){n.properties["#network_type"]=e.networkType}),PlatformAPI.getNetworkType({success:function(e){n.properties["#network_type"]=e.networkType},complete:function(){PlatformAPI.getSystemInfo({success:function(e){var t=e.system?e.system.replace(/\s+/g," ").split(" "):[],t={"#manufacturer":e.brand,"#device_model":e.model,"#screen_width":Number(e.screenWidth),"#screen_height":Number(e.screenHeight),"#os":t[0],"#os_version":t[1],"#mp_platform":e.mp_platform,"#system_language":e.systemLanguage};_.extend(n.properties,t),_.setMpPlatform(e.mp_platform)},complete:function(){e()}})}})}};
    
    // Platform API
    var PlatformAPI=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"_getCurrentPlatform",value:function(){return this.currentPlatform||(this.currentPlatform=PlatformProxy.createInstance())}},{key:"initConfig",value:function(e){this._getCurrentPlatform().initSdkConfig(e)}},{key:"getConfig",value:function(){return this._getCurrentPlatform().getConfig()}},{key:"getStorage",value:function(e,t,n){return this._getCurrentPlatform().getStorage(e,t,n)}},{key:"setStorage",value:function(e,t){return this._getCurrentPlatform().setStorage(e,t)}},{key:"removeStorage",value:function(e){return this._getCurrentPlatform().removeStorage(e)}},{key:"getSystemInfo",value:function(e){return this._getCurrentPlatform().getSystemInfo(e)}},{key:"getNetworkType",value:function(e){return this._getCurrentPlatform().getNetworkType(e)}},{key:"onNetworkStatusChange",value:function(e){this._getCurrentPlatform().onNetworkStatusChange(e)}},{key:"request",value:function(e){return this._getCurrentPlatform().request(e)}},{key:"setGlobal",value:function(e,t){e&&t&&this._getCurrentPlatform().setGlobal(e,t)}}]),e}();

    // HTTP Task for sending data
    var HttpTask=function(){function r(e,t,n,i,a){_classCallCheck(this,r),this.data=e,this.serverUrl=t,this.callback=a,this.tryCount=_.isNumber(n)?n:1,this.timeout=_.isNumber(i)?i:3e3,this.taClassName="HttpTask"}return _createClass(r,[{key:"run",value:function(){var t=this,e=_.createExtraHeaders();e["content-type"]="application/json",this.runTime=new Date,PlatformAPI.request({url:this.serverUrl,method:"POST",data:this.data,header:e,success:function(e){t.onSuccess(e)},fail:function(e){t.onFailed(e)}})}},{key:"onSuccess",value:function(e){if(!this.sendTimeout())if(_.isObject(e)&&200===e.statusCode){var t;switch((_.isUndefined(e.data)||_.isUndefined(e.data.code))&&(e.data={code:0}),e.data.code){case 0:t="success";break;case-1:t="invalid data";break;case-2:t="invalid APP ID";break;default:t="Unknown return code"}this.callback({code:e.data.code,msg:t})}else this.callback({code:-3,msg:_.isObject(e)?e.statusCode:"Unknown error"})}},{key:"onFailed",value:function(e){this.sendTimeout()||(0<--this.tryCount?this.run():this.callback({code:-3,msg:_.isObject(e)?e.errMsg:"Unknown error"}))}},{key:"sendTimeout",value:function(){return(new Date).getTime()-this.runTime.getTime()>this.timeout}}]),r}();

    // Sender Queue for managing HTTP requests
    var SenderQueue=function(){function e(){_classCallCheck(this,e),this.items=[],this.isRunning=!1,this.showDebug=!1}return _createClass(e,[{key:"enqueue",value:function(e,t,n,i){var a=this,i=!(3<arguments.length&&void 0!==i)||i,r=this,t=new HttpTask(JSON.stringify(e),t,n.maxRetries,n.sendTimeout,function(e){r.isRunning=!1,delete a.runTime,_.isFunction(n.callback)&&n.callback(e),r._runNext()});!0===i?(this.items.push(t),this._runNext()):t.run()}},{key:"_dequeue",value:function(){return this.items.shift()}},{key:"_runNext",value:function(){if(0<this.items.length&&!this.isRunning){this.isRunning=!0,this.runTime=new Date;var e=this.items.splice(0,this.items.length),t=e[0],n=JSON.parse(t.data),i=n["#app_id"],a=[];a.push(t.callback);for(var r=1;r<e.length;r++){var s=e[r],o=JSON.parse(s.data);o["#app_id"]===i&&t.serverUrl===s.serverUrl?(n.data=n.data.concat(o.data),a.push(s.callback)):this.items.push(s)}var c=(new Date).getTime();n["#flush_time"]=c,new HttpTask(JSON.stringify(n),t.serverUrl,t.tryCount,t.timeout,function(e){for(var t in a)Object.hasOwnProperty.call(a,t)&&(0,a[t])(e)}).run()}}},{key:"runTimeout",value:function(e){if(_.isDate(this.runTime)&&(new Date).getTime()-this.runTime.getTime()>e)return!0;return!1}},{key:"resetTimeout",value:function(){this.isRunning=!1,delete this.runTime}}]),e}();
    var senderQueue=new SenderQueue;

    // Data Persistence
    var ThinkingDataPersistence=function(){function e(t,n){var i=this;_classCallCheck(this,e),this.enabled=t.enablePersistence,this.enabled?(t.isChildInstance?(this.name=t.persistenceName+"_"+t.name,this.nameOld=t.persistenceNameOld+"_"+t.name):(this.name=t.persistenceName,this.nameOld=t.persistenceNameOld),t.asyncPersistence?(this._state={},PlatformAPI.getStorage(this.name,!0,function(e){_.isEmptyObject(e)?PlatformAPI.getStorage(i.nameOld,!0,function(e){i._state=_.extend2Layers({},e,i._state),i._init(t,n),i._save()}):(i._state=_.extend2Layers({},e,i._state),i._init(t,n),i._save())})):(this._state=PlatformAPI.getStorage(this.name)||{},_.isEmptyObject(this._state)&&(this._state=PlatformAPI.getStorage(this.nameOld)||{}),this._init(t,n))):(this._state={},this._init(t,n))}return _createClass(e,[{key:"_init",value:function(e,t){this.getDistinctId()||this.setDistinctId(_.UUID()),e.isChildInstance||(this.getDeviceId()||this._setDeviceId(_.UUID()),systemInformation.initDeviceId(this.getDeviceId())),this.initComplete=!0,"function"==typeof t&&t(),this._save()}},{key:"_save",value:function(){this.enabled&&this.initComplete&&PlatformAPI.setStorage(this.name,JSON.stringify(this._state))}},{key:"_set",value:function(e,t){var n,i=this;"string"==typeof e?(n={})[e]=t:"object"===_typeof(e)&&(n=e),_.each(n,function(e,t){i._state[t]=e}),this._save()}},{key:"_get",value:function(e){return this._state[e]}},{key:"setEventTimer",value:function(e,t){var n=this._state.event_timers||{};n[e]=t,this._set("event_timers",n)}},{key:"removeEventTimer",value:function(e){var t=(this._state.event_timers||{})[e];return _.isUndefined(t)||(delete this._state.event_timers[e],this._save()),t}},{key:"getDeviceId",value:function(){return this._state.device_id}},{key:"_setDeviceId",value:function(e){this.getDeviceId()?logger.warn("cannot modify the device id."):this._set("device_id",e)}},{key:"getDistinctId",value:function(){return this._state.distinct_id}},{key:"setDistinctId",value:function(e){this._set("distinct_id",e)}},{key:"getAccountId",value:function(){return this._state.account_id}},{key:"setAccountId",value:function(e){this._set("account_id",e)}},{key:"getSuperProperties",value:function(){return this._state.props||{}}},{key:"setSuperProperties",value:function(e,t){e=t?e:_.extend(this.getSuperProperties(),e);this._set("props",e)}}]),e}();

    // Main ThinkingDataAPI class
    var ThinkingDataAPI=function(){function n(e){_classCallCheck(this,n),e=e||{},e.appId=e.appId?_.checkAppId(e.appId):_.checkAppId(e.appid||""),e.serverUrl=e.serverUrl?_.checkUrl(e.serverUrl):_.checkUrl(e.server_url||"");var t=_.extend({},DEFAULT_CONFIG,PlatformAPI.getConfig());_.isObject(e)?this.config=_.extend(t,e):this.config=t,this._init(this.config)}return _createClass(n,[{key:"_init",value:function(e){var t=this;this.name=e.name,this.appId=e.appId||e.appid;var n=e.serverUrl||e.server_url;this.serverUrl=n+"/sync_xcx",this.serverDebugUrl=n+"/data_debug",this.configUrl=n+"/config",this.autoTrackProperties={},PlatformAPI.initConfig(e),this._queue=[],e.isChildInstance?this._state={}:(logger.enabled=e.enableLog,this.instances=[],this._state={getSystemInfo:!1,initComplete:!1},PlatformAPI.setGlobal(this,this.name)),this.store=new ThinkingDataPersistence(e,function(){t.config.asyncPersistence&&_.isFunction(t.config.persistenceComplete)&&t.config.persistenceComplete(t),t._updateState()}),this.enabled=!_.isBoolean(this.store._get("ta_enabled"))||this.store._get("ta_enabled"),this.isOptOut=!!_.isBoolean(this.store._get("ta_isOptOut"))&&this.store._get("ta_isOptOut")}},{key:"initSystemInfo",value:function(){var e=this;this.config.isChildInstance||systemInformation.getSystemInfo(function(){e._updateState({getSystemInfo:!0})})}},{key:"init",value:function(){if(this.initSystemInfo(),this._state.initComplete)return!1;this._updateState({initComplete:!0}),logger.info("TDAnalytics SDK initialize success, AppId = "+this.config.appId+", ServerUrl = "+this.config.serverUrl+", DeviceId = "+this.getDeviceId()+", Lib = "+Config.LIB_NAME+", LibVersion = "+Config.LIB_VERSION)}},{key:"_isReady",value:function(){return this._state.getSystemInfo&&this._state.initComplete&&this.store.initComplete&&this.getDeviceId()}},{key:"_updateState",value:function(e){var t=this;_.isObject(e)&&_.extend(this._state,e),this._onStateChange(),_.each(this.instances,function(e){t[e]._onStateChange()})}},{key:"_onStateChange",value:function(){var t=this;this._isReady()&&this._queue&&0<this._queue.length&&(_.each(this._queue,function(e){t[e[0]].apply(t,slice.call(e[1]))}),this._queue=[])}},{key:"_hasDisabled",value:function(){var e=!this.enabled||this.isOptOut;return e&&logger.info("SDK is Pause or Stop!"),e}},{key:"getDeviceId",value:function(){return this.store.getDeviceId()}},{key:"getDistinctId",value:function(){return this.store.getDistinctId()}},{key:"identify",value:function(e){if(this._hasDisabled())return;if(!PropertyChecker.userId(e))return logger.warn("identify failed due to invalid user id"),!1;this.store.setDistinctId(e)}},{key:"login",value:function(e){if(this._hasDisabled())return;if(!PropertyChecker.userId(e))return logger.warn("login failed due to invalid account id"),!1;this.store.setAccountId(e)}},{key:"logout",value:function(){this._hasDisabled()||this.store.setAccountId(null)}},{key:"setSuperProperties",value:function(e){this._hasDisabled()||PropertyChecker.properties(e)&&this.store.setSuperProperties(e)}},{key:"clearSuperProperties",value:function(){this._hasDisabled()||this.store.setSuperProperties({})}},{key:"getSuperProperties",value:function(){return this.store.getSuperProperties()}},{key:"timeEvent",value:function(e){this._hasDisabled()||PropertyChecker.event(e)&&this.store.setEventTimer(e,(new Date).getTime())}},{key:"enableTracking",value:function(){this.enabled=!0,this.store._set("ta_enabled",!0)}},{key:"disableTracking",value:function(){this.enabled=!1,this.store._set("ta_enabled",!1)}},{key:"optOutTracking",value:function(){this.isOptOut=!0,this.store._set("ta_isOptOut",!0)}},{key:"optInTracking",value:function(){this.isOptOut=!1,this.store._set("ta_isOptOut",!1)}},{key:"flush",value:function(){senderQueue._runNext()}}]),n}();

    // Add tracking methods to ThinkingDataAPI
    ThinkingDataAPI.prototype._sendRequest=function(e,t,n){if(!this._hasDisabled()){t=_.isDate(t)?t:new Date;var i={data:[{"#type":e.type,"#time":_.formatDate(_.formatTimeZone(t,this.config.zoneOffset)),"#distinct_id":this.store.getDistinctId()}]};this.store.getAccountId()&&(i.data[0]["#account_id"]=this.store.getAccountId()),"track"===e.type?(i.data[0]["#event_name"]=e.eventName,i.data[0].properties=_.extend({"#zone_offset":_.getTimeZone(t,this.config.zoneOffset)},systemInformation.properties,this.autoTrackProperties,this.store.getSuperProperties()),t=this.store.removeEventTimer(e.eventName),_.isUndefined(t)||(a=(new Date).getTime()-t,86400<(a=parseFloat((a/1e3).toFixed(3)))?a=86400:a<0&&(a=0),i.data[0].properties["#duration"]=a)):i.data[0].properties={},_.isObject(e.properties)&&!_.isEmptyObject(e.properties)&&_.extend(i.data[0].properties,e.properties),_.searchObjDate(i.data[0],this.config.zoneOffset),1<this.config.maxRetries&&(i.data[0]["#uuid"]=_.UUIDv4()),i["#app_id"]=this.appId,logger.info("Tracking data, "+JSON.stringify(i,null,4));var a=this.serverUrl;senderQueue.enqueue(i,a,{maxRetries:this.config.maxRetries,sendTimeout:this.config.sendTimeout,callback:e.onComplete,debugMode:this.config.debugMode,deviceId:this.getDeviceId()})}};

    ThinkingDataAPI.prototype._isObjectParams=function(e){return _.isObject(e)&&_.isFunction(e.onComplete)};

    ThinkingDataAPI.prototype.track=function(e,t,n,i){var a;this._hasDisabled()||(this._isObjectParams(e)&&(e=(a=e).eventName,t=a.properties,n=a.time,i=a.onComplete),PropertyChecker.event(e)&&PropertyChecker.properties(t)||!this.config.strict?this._internalTrack(e,t,n,i):_.isFunction(i)&&i({code:-1,msg:"invalid parameters"}))};

    ThinkingDataAPI.prototype._internalTrack=function(e,t,n,i){var a;this._hasDisabled()||(a=_.checkCalibration(t,n,this.config.enableCalibrationTime),n=_.isDate(n)?n:new Date,this._isReady()?this._sendRequest({type:"track",eventName:e,properties:a,onComplete:i},n):this._queue.push(["_internalTrack",[e,t,n,i]]))};

    // User property methods
    ThinkingDataAPI.prototype.userSet=function(e,t,n){var i;this._hasDisabled()||(this._isObjectParams(e)&&(e=(i=e).properties,t=i.time,n=i.onComplete),PropertyChecker.propertiesMust(e)||!this.config.strict?(t=_.isDate(t)?t:new Date,this._isReady()?this._sendRequest({type:"user_set",properties:e,onComplete:n},t):this._queue.push(["userSet",[e,t,n]])):(logger.warn("calling userSet failed due to invalid arguments"),_.isFunction(n)&&n({code:-1,msg:"invalid parameters"})))};

    ThinkingDataAPI.prototype.userSetOnce=function(e,t,n){var i;this._hasDisabled()||(this._isObjectParams(e)&&(e=(i=e).properties,t=i.time,n=i.onComplete),PropertyChecker.propertiesMust(e)||!this.config.strict?(t=_.isDate(t)?t:new Date,this._isReady()?this._sendRequest({type:"user_setOnce",properties:e,onComplete:n},t):this._queue.push(["userSetOnce",[e,t,n]])):(logger.warn("calling userSetOnce failed due to invalid arguments"),_.isFunction(n)&&n({code:-1,msg:"invalid parameters"})))};

    ThinkingDataAPI.prototype.userAdd=function(e,t,n){var i;this._hasDisabled()||(this._isObjectParams(e)&&(e=(i=e).properties,t=i.time,n=i.onComplete),PropertyChecker.userAddProperties(e)||!this.config.strict?(t=_.isDate(t)?t:new Date,this._isReady()?this._sendRequest({type:"user_add",properties:e,onComplete:n},t):this._queue.push(["userAdd",[e,t,n]])):(logger.warn("calling userAdd failed due to invalid arguments"),_.isFunction(n)&&n({code:-1,msg:"invalid parameters"})))};

    ThinkingDataAPI.prototype.userAppend=function(e,t,n){var i;this._hasDisabled()||(this._isObjectParams(e)&&(e=(i=e).properties,t=i.time,n=i.onComplete),PropertyChecker.userAppendProperties(e)||!this.config.strict?(t=_.isDate(t)?t:new Date,this._isReady()?this._sendRequest({type:"user_append",properties:e,onComplete:n},t):this._queue.push(["userAppend",[e,t,n]])):(logger.warn("calling userAppend failed due to invalid arguments"),_.isFunction(n)&&n({code:-1,msg:"invalid parameters"})))};

    ThinkingDataAPI.prototype.userUnset=function(e,t,n){var i;this._hasDisabled()||(this._isObjectParams(i)&&(e=i.property,t=i.time,n=i.onComplete),PropertyChecker.propertyName(e)||!this.config.strict?(t=_.isDate(t)?t:new Date,this._isReady()?((i={})[e]=0,this._sendRequest({type:"user_unset",properties:i,onComplete:n},t)):this._queue.push(["userUnset",[e,n,t]])):(logger.warn("calling userUnset failed due to invalid arguments"),_.isFunction(n)&&n({code:-1,msg:"invalid parameters"})))};

    ThinkingDataAPI.prototype.userDel=function(e,t){var n;this._hasDisabled()||(this._isObjectParams(e)&&(e=(n=e).time,t=n.onComplete),e=_.isDate(e)?e:new Date,this._isReady()?this._sendRequest({type:"user_del",onComplete:t},e):this._queue.push(["userDel",[e,t]]))};

    // Static method to create instance
    ThinkingDataAPI.init = function(config) {
        var instance = new ThinkingDataAPI(config);
        instance.init();
        return instance;
    };

    // Export the main class
    return ThinkingDataAPI;
}));
