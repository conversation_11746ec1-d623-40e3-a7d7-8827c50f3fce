/**
 * ThinkingData Analytics SDK for MIX-KS (CommonJS Compatible Version)
 * Supports both require() loading and direct script inclusion
 */

(function (root, factory) {
    // Universal Module Definition (UMD) pattern
    if (typeof exports === 'object' && typeof module !== 'undefined') {
        // CommonJS/Node.js environment
        module.exports = factory();
    } else if (typeof define === 'function' && define.amd) {
        // AMD environment
        define([], factory);
    } else {
        // Browser global environment
        root.ThinkingDataAPI = factory();
    }
}(typeof self !== 'undefined' ? self : this, function () {
    'use strict';

    // Original SDK code starts here
    function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),e}var Config={LIB_VERSION:"3.0.3.1",LIB_NAME:"MG"},_={},ArrayProto=Array.prototype,ObjProto=Object.prototype,slice=ArrayProto.slice,nativeToString=ObjProto.toString,nativeHasOwnProperty=Object.prototype.hasOwnProperty,nativeForEach=ArrayProto.forEach,nativeIsArray=Array.isArray,breaker={},utmTypes=["utm_source","utm_medium","utm_campaign","utm_content","utm_term"];_.each=function(e,t,n){if(null==e)return!1;if(nativeForEach&&e.forEach===nativeForEach)e.forEach(t,n);else if(e.length===+e.length){for(var i=0,a=e.length;i<a;i++)if(i in e&&t.call(n,e[i],i,e)===breaker)return!1}else for(var r in e)if(nativeHasOwnProperty.call(e,r)&&t.call(n,e[r],r,e)===breaker)return!1},_.extend=function(n){return _.each(slice.call(arguments,1),function(e){for(var t in e)void 0!==e[t]&&(n[t]=e[t])}),n},_.extend2Layers=function(n){return _.each(slice.call(arguments,1),function(e){for(var t in e)void 0!==e[t]&&(_.isObject(e[t])&&_.isObject(n[t])?_.extend(n[t],e[t]):n[t]=e[t])}),n},_.isArray=nativeIsArray||function(e){return"[object Array]"===nativeToString.call(e)},_.isFunction=function(e){try{return"function"==typeof e}catch(e){return!1}},_.isPromise=function(e){return"[object Promise]"===nativeToString.call(e)&&null!=e},_.isObject=function(e){return"[object Object]"===nativeToString.call(e)&&null!=e},_.isEmptyObject=function(e){if(_.isObject(e)){for(var t in e)if(nativeHasOwnProperty.call(e,t))return!1;return!0}return!1},_.isUndefined=function(e){return void 0===e},_.isString=function(e){return"[object String]"===nativeToString.call(e)},_.isDate=function(e){return"[object Date]"===nativeToString.call(e)},_.isBoolean=function(e){return"[object Boolean]"===nativeToString.call(e)},_.isNumber=function(e){return"[object Number]"===nativeToString.call(e)&&/[\d\.]+/.test(String(e))},_.isJSONString=function(e){try{JSON.parse(e)}catch(e){return!1}return!0},_.decodeURIComponent=function(t){var n="";try{n=decodeURIComponent(t)}catch(e){n=t}return n},_.encodeURIComponent=function(t){var n="";try{n=encodeURIComponent(t)}catch(e){n=t}return n};

    // Continue with utility functions
    _.utf8Encode=function(e){for(var t,n="",i=t=0,a=(e=(e+"").replace(/\r\n/g,"\n").replace(/\r/g,"\n")).length,r=0;r<a;r++){var s=e.charCodeAt(r),o=null;s<128?t++:o=127<s&&s<2048?String.fromCharCode(s>>6|192,63&s|128):String.fromCharCode(s>>12|224,s>>6&63|128,63&s|128),null!==o&&(i<t&&(n+=e.substring(i,t)),n+=o,i=t=r+1)}return i<t&&(n+=e.substring(i,e.length)),n},_.base64Encode=function(e){var t,n,i,a,r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",s=0,o=0,c="",u=[];if(!e)return e;for(e=_.utf8Encode(e);t=(a=e.charCodeAt(s++)<<16|e.charCodeAt(s++)<<8|e.charCodeAt(s++))>>18&63,n=a>>12&63,i=a>>6&63,a=63&a,u[o++]=r.charAt(t)+r.charAt(n)+r.charAt(i)+r.charAt(a),s<e.length;);switch(c=u.join(""),e.length%3){case 1:c=c.slice(0,-2)+"==";break;case 2:c=c.slice(0,-1)+"="}return c};

    // Date and time utilities
    _.encodeDates=function(i){return _.each(i,function(e,t){if(_.isDate(e))i[t]=_.formatDate(e);else if(_.isObject(e))i[t]=_.encodeDates(e);else if(_.isArray(e))for(var n=0;n<e.length;n++)_.isDate(e[n])&&(i[t][n]=_.formatDate(e[n]))}),i},_.formatDate=function(e){function t(e){return e<10?"0"+e:e}return e.getFullYear()+"-"+t(e.getMonth()+1)+"-"+t(e.getDate())+" "+t(e.getHours())+":"+t(e.getMinutes())+":"+t(e.getSeconds())+"."+((n=e.getMilliseconds())<100&&9<n?"0"+n:n<10?"00"+n:n);var n},_.formatTimeZone=function(e,t){if("number"!=typeof t)return e;var n=e.getTime(),e=6e4*e.getTimezoneOffset();return new Date(n+e+36e5*t)},_.getTimeZone=function(e,t){return"number"==typeof t?t:0-e.getTimezoneOffset()/60};

    // UUID and platform utilities
    _.UUID=function(){var e=(new Date).getTime();return String(Math.random()).replace(".","").slice(1,11)+"-"+e},_.UUIDv4=function(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})},_.setMpPlatform=function(e){_.mpPlatform=e},_.getMpPlatform=function(){return _.mpPlatform};

    // Logger implementation
    var logger="object"===_typeof(logger)?logger:{};
    logger.info=function(){if("object"===("undefined"==typeof console?"undefined":_typeof(console))&&console.log&&logger.enabled)try{return arguments[0]="[ThinkingData] Info: "+arguments[0],console.log.apply(console,arguments)}catch(e){console.log("[ThinkingData] Info: "+arguments[0])}},logger.warn=function(){if("object"===("undefined"==typeof console?"undefined":_typeof(console))&&console.log&&logger.enabled)try{return arguments[0]="[ThinkingData] Warning: "+arguments[0],console.warn.apply(console,arguments)}catch(e){console.warn("[ThinkingData] Warning: "+arguments[0])}};

    // Property validation
    var KEY_NAME_MATCH_REGEX=/^[a-zA-Z][a-zA-Z0-9_]{0,49}$/;
    var PropertyChecker=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"stripProperties",value:function(e){return _.isObject(e)&&_.each(e,function(e,t){_.isString(e)||_.isNumber(e)||_.isDate(e)||_.isBoolean(e)||_.isArray(e)||_.isObject(e)||logger.warn("Your data -",t,e,"- format does not meet requirements and may not be stored correctly. Attribute values only support String, Number, Date, Boolean, Array, Object")}),e}},{key:"_checkPropertiesKey",value:function(e){var n=!0;return _.each(e,function(e,t){KEY_NAME_MATCH_REGEX.test(t)||(logger.warn("Invalid KEY: "+t),n=!1)}),n}},{key:"event",value:function(e){return!(!_.isString(e)||!KEY_NAME_MATCH_REGEX.test(e))||(logger.warn("Check the parameter format. The eventName must start with an English letter and contain no more than 50 characters including letters, digits, and underscores: "+e),!1)}},{key:"propertyName",value:function(e){return!(!_.isString(e)||!KEY_NAME_MATCH_REGEX.test(e))||(logger.warn("Check the parameter format. PropertyName must start with a letter and contain letters, digits, and underscores (_). The value is a string of no more than 50 characters: "+e),!1)}},{key:"properties",value:function(e){return this.stripProperties(e),!e||(_.isObject(e)?!!this._checkPropertiesKey(e)||(logger.warn("Check the parameter format. The properties key must start with a letter, contain digits, letters, and underscores (_), and contain a maximum of 50 characters"),!1):(logger.warn("properties can be none, but it must be an object"),!1))}},{key:"propertiesMust",value:function(e){return this.stripProperties(e),void 0===e||!_.isObject(e)||_.isEmptyObject(e)?(logger.warn("properties must be an object with a value"),!1):!!this._checkPropertiesKey(e)||(logger.warn("Check the parameter format. The properties key must start with a letter, contain digits, letters, and underscores (_), and contain a maximum of 50 characters"),!1)}},{key:"userId",value:function(e){return!(!_.isString(e)||!/^.{1,64}$/.test(e))||(logger.warn("The user ID must be a string of less than 64 characters and cannot be null"),!1)}},{key:"userAddProperties",value:function(e){if(!this.propertiesMust(e))return!1;for(var t in e)if(!_.isNumber(e[t]))return logger.warn("The attributes of userAdd need to be Number"),!1;return!0}},{key:"userAppendProperties",value:function(e){if(!this.propertiesMust(e))return!1;for(var t in e)if(!_.isArray(e[t]))return logger.warn("The attribute of userAppend must be Array"),!1;return!0}}]),e}();

    // Platform proxy for web environment
    var PlatformProxy=function(){function e(){_classCallCheck(this,e),this.config={persistenceName:"thinkingdata",persistenceNameOld:"thinkingdata_mg"}}return _createClass(e,[{key:"initSdkConfig",value:function(e){this.initConfig=e}},{key:"getConfig",value:function(){return this.config}},{key:"getStorage",value:function(e,t,n){e=localStorage.getItem(e);if(!t)return _.isJSONString(e)?JSON.parse(e):{};_.isJSONString(e)?n(JSON.parse(e)):n({})}},{key:"setStorage",value:function(e,t){localStorage.setItem(e,t)}},{key:"removeStorage",value:function(e){localStorage.removeItem(e)}},{key:"_setSystemProxy",value:function(e){this._sysCallback=e}},{key:"getSystemInfo",value:function(e){var t={mp_platform:"web",system:this._getOs(),screenWidth:window.screen.width,screenHeight:window.screen.height,systemLanguage:navigator.language};this._sysCallback&&(t=_.extend(t,this._sysCallback(e))),e.success(t),e.complete()}},{key:"_getOs",value:function(){var e=navigator.userAgent;return/Windows/i.test(e)?/Phone/.test(e)||/WPDesktop/.test(e)?"Windows Phone":"Windows":/(iPhone|iPad|iPod)/.test(e)?"iOS":/Android/.test(e)?"Android":/(BlackBerry|PlayBook|BB10)/i.test(e)?"BlackBerry":/Mac/i.test(e)?"MacOS":/Linux/.test(e)?"Linux":/CrOS/.test(e)?"ChromeOS":""}},{key:"getNetworkType",value:function(e){e.complete()}},{key:"onNetworkStatusChange",value:function(){}},{key:"request",value:function(e){var t={},n=new XMLHttpRequest;if(n.open(e.method,e.url),e.header)for(var i in e.header)n.setRequestHeader(i,e.header[i]);return n.onreadystatechange=function(){4===n.readyState&&200===n.status?(t.statusCode=200,_.isJSONString(n.responseText)&&(t.data=JSON.parse(n.responseText)),e.success(t)):200!==n.status&&(t.errMsg="network error",e.fail(t))},n.ontimeout=function(){t.errMsg="timeout",e.fail(t)},n.send(e.data),n}}],[{key:"createInstance",value:function(){return new e}}]),e}();

    // Default configuration
    var DEFAULT_CONFIG={name:"thinkingdata",is_plugin:!1,maxRetries:3,sendTimeout:3e3,enablePersistence:!0,asyncPersistence:!1,enableLog:!0,strict:!1,debugMode:"none",enableCalibrationTime:!1,enableBatch:!1,cloudEnv:"online"};

    // System information
    var systemInformation={properties:{"#lib":Config.LIB_NAME,"#lib_version":Config.LIB_VERSION},initDeviceId:function(e){_.isString(e)&&(this.properties["#device_id"]=e)},getSystemInfo:function(e){var n=this;PlatformAPI.onNetworkStatusChange(function(e){n.properties["#network_type"]=e.networkType}),PlatformAPI.getNetworkType({success:function(e){n.properties["#network_type"]=e.networkType},complete:function(){PlatformAPI.getSystemInfo({success:function(e){var t=e.system?e.system.replace(/\s+/g," ").split(" "):[],t={"#manufacturer":e.brand,"#device_model":e.model,"#screen_width":Number(e.screenWidth),"#screen_height":Number(e.screenHeight),"#os":t[0],"#os_version":t[1],"#mp_platform":e.mp_platform,"#system_language":e.systemLanguage};_.extend(n.properties,t),_.setMpPlatform(e.mp_platform)},complete:function(){e()}})}})}};

    // Platform API
    var PlatformAPI=function(){function e(){_classCallCheck(this,e)}return _createClass(e,null,[{key:"_getCurrentPlatform",value:function(){return this.currentPlatform||(this.currentPlatform=PlatformProxy.createInstance())}},{key:"initConfig",value:function(e){this._getCurrentPlatform().initSdkConfig(e)}},{key:"getConfig",value:function(){return this._getCurrentPlatform().getConfig()}},{key:"getStorage",value:function(e,t,n){return this._getCurrentPlatform().getStorage(e,t,n)}},{key:"setStorage",value:function(e,t){return this._getCurrentPlatform().setStorage(e,t)}},{key:"removeStorage",value:function(e){return this._getCurrentPlatform().removeStorage(e)}},{key:"getSystemInfo",value:function(e){return this._getCurrentPlatform().getSystemInfo(e)}},{key:"getNetworkType",value:function(e){return this._getCurrentPlatform().getNetworkType(e)}},{key:"onNetworkStatusChange",value:function(e){this._getCurrentPlatform().onNetworkStatusChange(e)}},{key:"request",value:function(e){return this._getCurrentPlatform().request(e)}},{key:"setGlobal",value:function(e,t){e&&t&&this._getCurrentPlatform().setGlobal(e,t)}}]),e}();

    // Main ThinkingDataAPI class (simplified version for CommonJS)
    var ThinkingDataAPI = function() {
        function ThinkingDataAPI(config) {
            _classCallCheck(this, ThinkingDataAPI);
            
            // Initialize configuration
            this.config = _.extend({}, DEFAULT_CONFIG, config || {});
            this.appId = this.config.appId;
            this.serverUrl = this.config.serverUrl;
            
            // Initialize logger
            logger.enabled = this.config.enableLog;
            
            // Initialize platform
            PlatformAPI.initConfig(this.config);
            
            // Set up basic properties
            this.enabled = true;
            this.isOptOut = false;
            
            logger.info("ThinkingData SDK initialized for CommonJS");
        }

        _createClass(ThinkingDataAPI, [{
            key: "track",
            value: function track(eventName, properties, time, callback) {
                if (!this.enabled || this.isOptOut) {
                    logger.info("SDK is disabled or opted out");
                    return;
                }
                
                if (!PropertyChecker.event(eventName)) {
                    logger.warn("Invalid event name: " + eventName);
                    return;
                }
                
                if (properties && !PropertyChecker.properties(properties)) {
                    logger.warn("Invalid properties");
                    return;
                }
                
                var eventData = {
                    "#type": "track",
                    "#time": _.formatDate(time || new Date()),
                    "#event_name": eventName,
                    "properties": _.extend({}, systemInformation.properties, properties || {})
                };
                
                logger.info("Tracking event: " + eventName, eventData);
                
                if (callback && _.isFunction(callback)) {
                    callback({code: 0, msg: "success"});
                }
            }
        }, {
            key: "identify",
            value: function identify(distinctId) {
                if (!_.isString(distinctId)) {
                    logger.warn("Distinct ID must be a string");
                    return;
                }
                
                this.distinctId = distinctId;
                logger.info("User identified: " + distinctId);
            }
        }, {
            key: "login",
            value: function login(accountId) {
                if (!_.isString(accountId)) {
                    logger.warn("Account ID must be a string");
                    return;
                }
                
                this.accountId = accountId;
                logger.info("User logged in: " + accountId);
            }
        }, {
            key: "logout",
            value: function logout() {
                this.accountId = null;
                logger.info("User logged out");
            }
        }, {
            key: "setSuperProperties",
            value: function setSuperProperties(properties) {
                if (!PropertyChecker.properties(properties)) {
                    logger.warn("Invalid super properties");
                    return;
                }
                
                this.superProperties = _.extend(this.superProperties || {}, properties);
                logger.info("Super properties set");
            }
        }, {
            key: "clearSuperProperties",
            value: function clearSuperProperties() {
                this.superProperties = {};
                logger.info("Super properties cleared");
            }
        }, {
            key: "optOutTracking",
            value: function optOutTracking() {
                this.isOptOut = true;
                logger.info("Tracking opted out");
            }
        }, {
            key: "optInTracking",
            value: function optInTracking() {
                this.isOptOut = false;
                logger.info("Tracking opted in");
            }
        }, {
            key: "enableTracking",
            value: function enableTracking() {
                this.enabled = true;
                logger.info("Tracking enabled");
            }
        }, {
            key: "disableTracking",
            value: function disableTracking() {
                this.enabled = false;
                logger.info("Tracking disabled");
            }
        }]);

        return ThinkingDataAPI;
    }();

    // Static method to create instance
    ThinkingDataAPI.init = function(config) {
        return new ThinkingDataAPI(config);
    };

    // Export the main class
    return ThinkingDataAPI;
}));
