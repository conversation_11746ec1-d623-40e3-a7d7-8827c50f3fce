/**
 * ThinkingData SDK Usage Example
 * 展示如何使用支持require加载的SDK
 */

// CommonJS 环境使用方式 (Node.js)
const ThinkingDataAPI = require('./sdk-MIX-KS-require.js');

// 初始化SDK
const ta = ThinkingDataAPI.init({
    appId: 'your_app_id',
    serverUrl: 'https://your-server.com',
    enableLog: true,
    debugMode: 'none', // 'none', 'debug', 'debugOnly'
    enablePersistence: true,
    strict: false
});

// 基础事件追踪
ta.track('test_event', {
    property1: 'value1',
    property2: 123,
    property3: true,
    property4: new Date()
});

// 用户识别
ta.identify('user_12345');

// 用户登录
ta.login('account_67890');

// 设置公共属性
ta.setSuperProperties({
    platform: 'web',
    version: '1.0.0'
});

// 用户属性设置
ta.userSet({
    name: '张三',
    age: 25,
    city: '北京'
});

// 用户属性设置一次（如果已存在则不覆盖）
ta.userSetOnce({
    first_visit_time: new Date(),
    register_source: 'organic'
});

// 用户数值属性累加
ta.userAdd({
    login_count: 1,
    total_revenue: 99.99
});

// 用户数组属性追加
ta.userAppend({
    favorite_categories: ['electronics', 'books']
});

// 删除用户属性
ta.userUnset('temp_property');

// 删除用户
// ta.userDel();

// 事件计时
ta.timeEvent('page_view');
// ... 用户浏览页面
setTimeout(() => {
    ta.track('page_view', {
        page_name: 'home',
        page_url: '/home'
    });
}, 2000);

// 获取设备ID
console.log('Device ID:', ta.getDeviceId());

// 获取用户ID
console.log('Distinct ID:', ta.getDistinctId());

// 获取公共属性
console.log('Super Properties:', ta.getSuperProperties());

// 清除公共属性
// ta.clearSuperProperties();

// 暂停追踪
// ta.disableTracking();

// 恢复追踪
// ta.enableTracking();

// 选择退出追踪
// ta.optOutTracking();

// 选择加入追踪
// ta.optInTracking();

// 立即发送数据
ta.flush();

// 浏览器环境使用方式
/*
<script src="sdk-MIX-KS-require.js"></script>
<script>
    const ta = ThinkingDataAPI.init({
        appId: 'your_app_id',
        serverUrl: 'https://your-server.com',
        enableLog: true
    });
    
    ta.track('page_view', {
        page_name: 'home'
    });
</script>
*/

// AMD 环境使用方式
/*
define(['sdk-MIX-KS-require'], function(ThinkingDataAPI) {
    const ta = ThinkingDataAPI.init({
        appId: 'your_app_id',
        serverUrl: 'https://your-server.com'
    });
    
    ta.track('module_loaded', {
        module_name: 'analytics'
    });
});
*/

module.exports = ta;
